<?php
$page_title = "Employee Registration";
include '../includes/header.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $first_name = sanitizeInput($_POST['first_name'] ?? '');
    $last_name = sanitizeInput($_POST['last_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $employee_id = sanitizeInput($_POST['employee_id'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $department = sanitizeInput($_POST['department'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $terms_accepted = isset($_POST['terms_accepted']);
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    $errors = [];
    
    // Validate CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        $errors[] = 'Security token mismatch. Please try again.';
    }
    
    // Validate required fields
    if (empty($first_name)) $errors[] = 'First name is required.';
    if (empty($last_name)) $errors[] = 'Last name is required.';
    if (empty($email)) $errors[] = 'Email address is required.';
    if (empty($employee_id)) $errors[] = 'Employee ID is required.';
    if (empty($phone)) $errors[] = 'Phone number is required.';
    if (empty($department)) $errors[] = 'Department is required.';
    if (empty($password)) $errors[] = 'Password is required.';
    if (empty($confirm_password)) $errors[] = 'Password confirmation is required.';
    
    // Validate email format
    if (!empty($email) && !validateEmail($email)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    // Validate password strength
    if (!empty($password) && !validatePassword($password)) {
        $errors[] = 'Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number.';
    }
    
    // Validate password confirmation
    if (!empty($password) && !empty($confirm_password) && $password !== $confirm_password) {
        $errors[] = 'Passwords do not match.';
    }
    
    // Validate employee ID format
    if (!empty($employee_id) && strlen($employee_id) < 3) {
        $errors[] = 'Employee ID must be at least 3 characters long.';
    }
    
    // Validate terms acceptance
    if (!$terms_accepted) {
        $errors[] = 'You must accept the terms and conditions to register.';
    }
    
    if (empty($errors)) {
        // TODO: Implement actual registration logic here
        // For now, this is just a template structure
        
        // Example registration logic (replace with actual database insertion)
        // Check if email or employee ID already exists
        // Hash password securely
        // Insert into database
        // Send welcome email
        
        setFlashMessage('Registration successful! Please check your email for account activation instructions.', 'success');
        redirect('employee-login.php');
    } else {
        foreach ($errors as $error) {
            setFlashMessage($error, 'danger');
            break; // Show only first error
        }
    }
}
?>

<div class="auth-container">
    <div class="auth-card" style="max-width: 500px;">
        <div class="auth-header">
            <h2><i class="fas fa-user-plus"></i> Employee Registration</h2>
            <div class="role-badge">New Employee Signup</div>
        </div>
        
        <div class="auth-body">
            <?php displayFlashMessage(); ?>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Welcome!</strong> Please fill out all required fields to create your employee account.
            </div>
            
            <form method="POST" class="auth-form" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="first_name" class="form-label">First Name *</label>
                            <input 
                                type="text" 
                                id="first_name" 
                                name="first_name" 
                                class="form-control" 
                                placeholder="Enter your first name"
                                value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>"
                                required
                                autocomplete="given-name"
                            >
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="last_name" class="form-label">Last Name *</label>
                            <input 
                                type="text" 
                                id="last_name" 
                                name="last_name" 
                                class="form-control" 
                                placeholder="Enter your last name"
                                value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>"
                                required
                                autocomplete="family-name"
                            >
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="email" class="form-label">Email Address *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-control" 
                            placeholder="Enter your email address"
                            value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                            required
                            autocomplete="email"
                        >
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="employee_id" class="form-label">Employee ID *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-id-badge"></i>
                                </span>
                                <input 
                                    type="text" 
                                    id="employee_id" 
                                    name="employee_id" 
                                    class="form-control" 
                                    placeholder="Enter your employee ID"
                                    value="<?php echo htmlspecialchars($_POST['employee_id'] ?? ''); ?>"
                                    required
                                >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone" class="form-label">Phone Number *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                <input 
                                    type="tel" 
                                    id="phone" 
                                    name="phone" 
                                    class="form-control" 
                                    placeholder="Enter your phone number"
                                    value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                    required
                                    autocomplete="tel"
                                >
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="department" class="form-label">Department *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-building"></i>
                        </span>
                        <select 
                            id="department" 
                            name="department" 
                            class="form-control" 
                            required
                        >
                            <option value="">Select your department</option>
                            <option value="hr" <?php echo ($_POST['department'] ?? '') === 'hr' ? 'selected' : ''; ?>>Human Resources</option>
                            <option value="it" <?php echo ($_POST['department'] ?? '') === 'it' ? 'selected' : ''; ?>>Information Technology</option>
                            <option value="finance" <?php echo ($_POST['department'] ?? '') === 'finance' ? 'selected' : ''; ?>>Finance</option>
                            <option value="marketing" <?php echo ($_POST['department'] ?? '') === 'marketing' ? 'selected' : ''; ?>>Marketing</option>
                            <option value="sales" <?php echo ($_POST['department'] ?? '') === 'sales' ? 'selected' : ''; ?>>Sales</option>
                            <option value="operations" <?php echo ($_POST['department'] ?? '') === 'operations' ? 'selected' : ''; ?>>Operations</option>
                            <option value="other" <?php echo ($_POST['department'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Password *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control" 
                            placeholder="Create a strong password"
                            required
                            autocomplete="new-password"
                        >
                        <button type="button" class="password-toggle" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #7f8c8d; z-index: 3;">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small class="text-muted">
                        Password must be at least 8 characters with uppercase, lowercase, and number.
                    </small>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password" class="form-label">Confirm Password *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input 
                            type="password" 
                            id="confirm_password" 
                            name="confirm_password" 
                            class="form-control" 
                            placeholder="Confirm your password"
                            required
                            autocomplete="new-password"
                        >
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input 
                            type="checkbox" 
                            id="terms_accepted" 
                            name="terms_accepted" 
                            class="form-check-input" 
                            value="1"
                            required
                            <?php echo isset($_POST['terms_accepted']) ? 'checked' : ''; ?>
                        >
                        <label for="terms_accepted" class="form-check-label">
                            I agree to the <a href="terms.php" target="_blank">Terms and Conditions</a> and <a href="privacy.php" target="_blank">Privacy Policy</a> *
                        </label>
                    </div>
                </div>
                
                <button type="submit" class="btn-auth btn-success">
                    <i class="fas fa-user-plus"></i> Create Account
                </button>
            </form>
        </div>
        
        <div class="auth-footer">
            <div class="mb-2">
                <a href="employee-login.php">
                    <i class="fas fa-sign-in-alt"></i> Already have an account? Sign in
                </a>
            </div>
            <div>
                <a href="../index.php">
                    <i class="fas fa-arrow-left"></i> Back to role selection
                </a>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
