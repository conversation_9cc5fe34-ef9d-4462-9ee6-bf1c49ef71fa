<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Configuration
$site_title = "Corporate Auth System";
$bootstrap_version = "5.3.2";
$fontawesome_version = "6.4.0";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="theme-color" content="#2c3e50">
    <meta name="description" content="Corporate Authentication System - Secure login portal for employees, administrators, and PDC members">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . $site_title : $site_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@<?php echo $bootstrap_version; ?>/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/<?php echo $fontawesome_version; ?>/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetPath('css/auth-styles.css'); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo getAssetPath('images/favicon.ico'); ?>">
    
    <!-- Additional head content -->
    <?php if (isset($additional_head)): ?>
        <?php echo $additional_head; ?>
    <?php endif; ?>
</head>
<body>

<?php
/**
 * Helper function to get asset path
 */
function getAssetPath($path) {
    // Determine the relative path to assets based on current directory
    $current_dir = dirname($_SERVER['PHP_SELF']);
    $asset_base = '';
    
    // If we're in a subdirectory, adjust the path
    if (strpos($current_dir, '/auth') !== false) {
        $asset_base = '../';
    }
    
    return $asset_base . 'assets/' . $path;
}

/**
 * Helper function to get base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_name = dirname($_SERVER['SCRIPT_NAME']);
    
    // Remove /auth from the path if present
    $script_name = str_replace('/auth', '', $script_name);
    
    return $protocol . '://' . $host . $script_name;
}

/**
 * Helper function to redirect
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * Helper function to sanitize input
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Helper function to validate email
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Helper function to validate password strength
 */
function validatePassword($password) {
    // At least 8 characters, one uppercase, one lowercase, one number
    return strlen($password) >= 8 && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/\d/', $password);
}

/**
 * Helper function to generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Helper function to verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Helper function to display flash messages
 */
function displayFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        
        echo '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">';
        echo htmlspecialchars($message);
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
        
        // Clear the flash message
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
    }
}

/**
 * Helper function to set flash message
 */
function setFlashMessage($message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
}

/**
 * Helper function to check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

/**
 * Helper function to get current user role
 */
function getUserRole() {
    return $_SESSION['user_role'] ?? null;
}

/**
 * Helper function to require login
 */
function requireLogin($redirect_url = null) {
    if (!isLoggedIn()) {
        if ($redirect_url) {
            redirect($redirect_url);
        } else {
            redirect(getBaseUrl() . '/index.php');
        }
    }
}

/**
 * Helper function to require specific role
 */
function requireRole($required_role, $redirect_url = null) {
    requireLogin($redirect_url);
    
    if (getUserRole() !== $required_role) {
        setFlashMessage('Access denied. Insufficient privileges.', 'danger');
        if ($redirect_url) {
            redirect($redirect_url);
        } else {
            redirect(getBaseUrl() . '/index.php');
        }
    }
}
?>
