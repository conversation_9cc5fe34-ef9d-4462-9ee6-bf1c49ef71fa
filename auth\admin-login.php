<?php
$page_title = "Admin/HR Login";
include '../includes/header.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Validate CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        setFlashMessage('Security token mismatch. Please try again.', 'danger');
    }
    // Validate input
    elseif (empty($username) || empty($password)) {
        setFlashMessage('Please fill in all required fields.', 'danger');
    }
    else {
        // TODO: Implement actual authentication logic here
        // For now, this is just a template structure
        
        // Example authentication logic (replace with actual database check)
        if ($username === 'admin' && $password === 'admin123') {
            // Set session variables
            $_SESSION['user_id'] = 2;
            $_SESSION['user_role'] = 'admin';
            $_SESSION['user_username'] = $username;
            $_SESSION['user_name'] = 'Admin User';
            
            // Handle remember me functionality
            if ($remember_me) {
                // Set secure cookie (implement proper token-based remember me)
                setcookie('remember_token', 'secure_admin_token_here', time() + (30 * 24 * 60 * 60), '/', '', true, true);
            }
            
            setFlashMessage('Admin login successful! Welcome to the admin panel.', 'success');
            redirect('../dashboard/admin-dashboard.php');
        } else {
            setFlashMessage('Invalid username or password. Please try again.', 'danger');
        }
    }
}
?>

<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
            <h2><i class="fas fa-user-shield"></i> Admin/HR Login</h2>
            <div class="role-badge">Administrative Access</div>
        </div>
        
        <div class="auth-body">
            <?php displayFlashMessage(); ?>
            
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Restricted Access:</strong> This portal is for authorized administrative personnel only.
            </div>
            
            <form method="POST" class="auth-form" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label for="username" class="form-label">Username *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user-shield"></i>
                        </span>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            class="form-control" 
                            placeholder="Enter your admin username"
                            value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                            required
                            autocomplete="username"
                        >
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Password *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control" 
                            placeholder="Enter your password"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #7f8c8d; z-index: 3;">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="remember-me">
                    <input 
                        type="checkbox" 
                        id="remember_me" 
                        name="remember_me" 
                        value="1"
                        <?php echo isset($_POST['remember_me']) ? 'checked' : ''; ?>
                    >
                    <label for="remember_me">Keep me signed in</label>
                </div>
                
                <button type="submit" class="btn-auth" style="background: #e74c3c; color: white;">
                    <i class="fas fa-sign-in-alt"></i> Admin Sign In
                </button>
            </form>
            
            <div class="mt-3 p-3 bg-light rounded">
                <small class="text-muted">
                    <i class="fas fa-shield-alt"></i>
                    <strong>Security Notice:</strong> All admin activities are logged and monitored for security purposes.
                </small>
            </div>
        </div>
        
        <div class="auth-footer">
            <div class="mb-2">
                <a href="admin-forgot-password.php">
                    <i class="fas fa-key"></i> Forgot your admin password?
                </a>
            </div>
            <div>
                <a href="../index.php">
                    <i class="fas fa-arrow-left"></i> Back to role selection
                </a>
            </div>
        </div>
    </div>
</div>

<?php
$additional_scripts = '
<script>
// Demo credentials helper for admin
document.addEventListener("DOMContentLoaded", function() {
    // Add demo credentials info for testing
    const demoInfo = document.createElement("div");
    demoInfo.className = "alert alert-info mt-3";
    demoInfo.innerHTML = `
        <strong><i class="fas fa-info-circle"></i> Demo Credentials:</strong><br>
        Username: admin<br>
        Password: admin123
    `;
    
    const authBody = document.querySelector(".auth-body");
    authBody.appendChild(demoInfo);
    
    // Enhanced security warning
    setTimeout(function() {
        const securityWarning = document.createElement("div");
        securityWarning.className = "alert alert-danger";
        securityWarning.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Security Reminder:</strong> Please ensure you are on a secure network and log out when finished.
        `;
        
        const form = document.querySelector(".auth-form");
        form.parentNode.insertBefore(securityWarning, form);
    }, 2000);
});
</script>
';

include '../includes/footer.php';
?>
