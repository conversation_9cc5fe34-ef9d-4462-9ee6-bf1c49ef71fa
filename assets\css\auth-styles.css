/* Corporate Authentication System Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --dark-text: #2c3e50;
    --light-text: #6c757d;
    --border-color: #dee2e6;
    --shadow: 0 4px 20px rgba(0,0,0,0.08);
    --shadow-hover: 0 8px 30px rgba(0,0,0,0.12);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    color: var(--dark-text);
}

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

.auth-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    width: 100%;
    max-width: 420px;
    animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.auth-header {
    background: var(--primary-color);
    color: white;
    padding: 32px 24px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.auth-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.auth-header h2 {
    margin: 0;
    font-weight: 400;
    font-size: 1.75rem;
    letter-spacing: -0.025em;
    position: relative;
    z-index: 1;
}

.auth-header .role-badge {
    display: inline-block;
    background: rgba(255,255,255,0.25);
    padding: 6px 16px;
    border-radius: 24px;
    font-size: 0.875rem;
    margin-top: 12px;
    font-weight: 500;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(255,255,255,0.2);
}

.auth-body {
    padding: 32px 28px;
}

.form-group {
    margin-bottom: 24px;
}

.form-label {
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: 8px;
    display: block;
    font-size: 0.9rem;
    letter-spacing: 0.025em;
}

.form-control {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: #fff;
    font-family: inherit;
    line-height: 1.5;
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.08);
    transform: translateY(-1px);
}

.form-control:hover:not(:focus) {
    border-color: #adb5bd;
}

.form-control.is-invalid {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 4px rgba(231, 76, 60, 0.08);
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 4px rgba(39, 174, 96, 0.08);
}

.invalid-feedback {
    color: var(--accent-color);
    font-size: 0.875rem;
    margin-top: 6px;
    display: block;
    font-weight: 500;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-4px); }
    to { opacity: 1; transform: translateY(0); }
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group-text {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--light-text);
    z-index: 2;
    font-size: 1rem;
    transition: var(--transition);
}

.input-group .form-control {
    padding-left: 48px;
}

.input-group .form-control:focus + .input-group-text,
.input-group .form-control:focus ~ .input-group-text {
    color: var(--secondary-color);
}

.btn-auth {
    width: 100%;
    padding: 16px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-transform: none;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
    font-family: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-height: 52px;
}

.btn-auth::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-auth:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #229954 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954 0%, #1e7e34 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.auth-footer {
    text-align: center;
    padding: 24px 28px;
    background: var(--light-bg);
    border-top: 1px solid var(--border-color);
}

.auth-footer a {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 0;
}

.auth-footer a:hover {
    color: #2980b9;
    text-decoration: none;
    transform: translateX(2px);
}

.auth-footer > div {
    margin-bottom: 8px;
}

.auth-footer > div:last-child {
    margin-bottom: 0;
}

.role-selector {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 48px 40px;
    text-align: center;
    max-width: 600px;
    width: 100%;
    animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.role-selector h1 {
    color: var(--primary-color);
    margin-bottom: 12px;
    font-weight: 400;
    font-size: 2.25rem;
    letter-spacing: -0.025em;
}

.role-selector > p {
    color: var(--light-text);
    margin-bottom: 32px;
    font-size: 1.1rem;
}

.role-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    margin-top: 32px;
}

.role-grid .role-card {
    animation: slideInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.role-grid .role-card:nth-child(1) { animation-delay: 0.1s; }
.role-grid .role-card:nth-child(2) { animation-delay: 0.2s; }
.role-grid .role-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.role-card {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 28px 24px;
    text-decoration: none;
    color: var(--dark-text);
    transition: var(--transition);
    display: block;
    position: relative;
    overflow: hidden;
    background: white;
}

.role-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.05), transparent);
    transition: left 0.5s;
}

.role-card:hover::before {
    left: 100%;
}

.role-card:hover {
    border-color: var(--secondary-color);
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
    text-decoration: none;
    color: var(--dark-text);
}

.role-card i {
    font-size: 2.75rem;
    margin-bottom: 16px;
    color: var(--secondary-color);
    transition: var(--transition);
}

.role-card:hover i {
    transform: scale(1.1);
    color: #2980b9;
}

.role-card h3 {
    margin: 0 0 8px 0;
    font-weight: 600;
    font-size: 1.25rem;
}

.role-card p {
    margin: 0;
    color: var(--light-text);
    font-size: 0.95rem;
    line-height: 1.5;
}

.alert {
    padding: 16px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 24px;
    border: 1px solid transparent;
    font-size: 0.95rem;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: fadeIn 0.3s ease;
}

.alert i {
    margin-top: 2px;
    flex-shrink: 0;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f1aeb5;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    border-color: #badbcc;
    color: #155724;
}

.alert-info {
    background-color: #cce7f0;
    border-color: #b3d7e6;
    color: #055160;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #664d03;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
    padding: 4px 0;
}

.remember-me input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--secondary-color);
    cursor: pointer;
}

.remember-me label {
    font-size: 0.95rem;
    color: var(--dark-text);
    cursor: pointer;
    margin: 0;
}

/* Responsive Design */
@media (min-width: 768px) {
    .role-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }

    .auth-card {
        max-width: 480px;
    }

    .role-selector {
        padding: 56px 48px;
    }
}

@media (min-width: 992px) {
    .auth-container {
        padding: 40px;
    }

    .role-selector {
        max-width: 700px;
    }
}

@media (max-width: 767px) {
    .auth-container {
        padding: 16px;
        align-items: flex-start;
        padding-top: 40px;
    }

    .auth-body {
        padding: 24px 20px;
    }

    .auth-header {
        padding: 24px 20px;
    }

    .auth-header h2 {
        font-size: 1.5rem;
    }

    .role-selector {
        padding: 32px 24px;
    }

    .role-selector h1 {
        font-size: 1.875rem;
    }

    .role-card {
        padding: 24px 20px;
    }

    .role-card i {
        font-size: 2.25rem;
    }
}

@media (max-width: 480px) {
    .auth-container {
        padding: 12px;
        padding-top: 20px;
    }

    .auth-body {
        padding: 20px 16px;
    }

    .auth-header {
        padding: 20px 16px;
    }

    .auth-footer {
        padding: 20px 16px;
    }

    .role-selector {
        padding: 24px 16px;
    }

    .btn-auth {
        padding: 14px 20px;
        font-size: 0.95rem;
    }

    .form-control {
        padding: 12px 14px;
    }

    .input-group .form-control {
        padding-left: 44px;
    }

    .input-group-text {
        left: 14px;
        font-size: 0.9rem;
    }
}

/* Password Toggle */
.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--light-text);
    z-index: 3;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: var(--transition);
    font-size: 1rem;
}

.password-toggle:hover {
    color: var(--secondary-color);
    background: rgba(52, 152, 219, 0.1);
}

/* Select Styling */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
    appearance: none;
}

/* Form Check Styling */
.form-check {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 0;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin: 0;
    accent-color: var(--secondary-color);
    cursor: pointer;
}

.form-check-label {
    font-size: 0.95rem;
    color: var(--dark-text);
    cursor: pointer;
    line-height: 1.5;
    margin: 0;
}

.form-check-label a {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 500;
}

.form-check-label a:hover {
    text-decoration: underline;
}

/* Loading States */
.btn-auth:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-auth.loading {
    position: relative;
    color: transparent;
}

.btn-auth.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Focus Management */
.auth-form {
    position: relative;
}

.form-group:focus-within .form-label {
    color: var(--secondary-color);
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .form-control {
        border-width: 3px;
    }

    .btn-auth {
        border: 2px solid currentColor;
    }
}

/* Print Styles */
@media print {
    .auth-container {
        background: white !important;
    }

    .auth-card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }

    .password-toggle {
        display: none !important;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    :root {
        --light-bg: #1a1a1a;
        --dark-text: #e0e0e0;
        --light-text: #a0a0a0;
        --border-color: #404040;
    }
}

/* Enhanced Focus Styles for Better Accessibility */
.form-control:focus,
.btn-auth:focus,
.password-toggle:focus,
.role-card:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Better Text Rendering */
body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}
