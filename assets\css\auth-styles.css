/* Corporate Authentication System Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --light-bg: #ecf0f1;
    --dark-text: #2c3e50;
    --light-text: #7f8c8d;
    --border-color: #bdc3c7;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.auth-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    width: 100%;
    max-width: 400px;
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header {
    background: var(--primary-color);
    color: white;
    padding: 30px 20px;
    text-align: center;
}

.auth-header h2 {
    margin: 0;
    font-weight: 300;
    font-size: 1.8rem;
}

.auth-header .role-badge {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-top: 10px;
}

.auth-body {
    padding: 40px 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: 8px;
    display: block;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control.is-invalid {
    border-color: var(--accent-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.invalid-feedback {
    color: var(--accent-color);
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.input-group {
    position: relative;
}

.input-group-text {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--light-text);
    z-index: 2;
}

.input-group .form-control {
    padding-left: 45px;
}

.btn-auth {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #229954;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.auth-footer {
    text-align: center;
    padding: 20px 30px;
    background: var(--light-bg);
    border-top: 1px solid var(--border-color);
}

.auth-footer a {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

.role-selector {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 40px;
    text-align: center;
    max-width: 500px;
    width: 100%;
}

.role-selector h1 {
    color: var(--primary-color);
    margin-bottom: 30px;
    font-weight: 300;
}

.role-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 30px;
}

.role-card {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    text-decoration: none;
    color: var(--dark-text);
    transition: all 0.3s ease;
    display: block;
}

.role-card:hover {
    border-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow);
    text-decoration: none;
    color: var(--dark-text);
}

.role-card i {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--secondary-color);
}

.role-card h3 {
    margin: 0 0 10px 0;
    font-weight: 600;
}

.role-card p {
    margin: 0;
    color: var(--light-text);
    font-size: 0.9rem;
}

.alert {
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.remember-me input[type="checkbox"] {
    width: auto;
}

/* Responsive Design */
@media (min-width: 768px) {
    .role-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .auth-card {
        max-width: 450px;
    }
}

@media (max-width: 576px) {
    .auth-body {
        padding: 30px 20px;
    }
    
    .auth-header {
        padding: 25px 20px;
    }
    
    .auth-header h2 {
        font-size: 1.5rem;
    }
}

/* Loading States */
.btn-auth:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-auth.loading {
    position: relative;
    color: transparent;
}

.btn-auth.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
