<?php
$page_title = "Forgot Password";
include '../includes/header.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Validate CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        setFlashMessage('Security token mismatch. Please try again.', 'danger');
    }
    // Validate input
    elseif (empty($email)) {
        setFlashMessage('Please enter your email address.', 'danger');
    }
    elseif (!validateEmail($email)) {
        setFlashMessage('Please enter a valid email address.', 'danger');
    }
    else {
        // TODO: Implement actual password reset logic here
        // For now, this is just a template structure
        
        // Example password reset logic:
        // 1. Check if email exists in database
        // 2. Generate secure reset token
        // 3. Store token with expiration time
        // 4. Send reset email
        
        setFlashMessage('If an account with that email exists, you will receive password reset instructions shortly.', 'success');
        // Don't redirect immediately to prevent email enumeration
    }
}
?>

<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h2><i class="fas fa-key"></i> Forgot Password</h2>
            <div class="role-badge">Password Recovery</div>
        </div>
        
        <div class="auth-body">
            <?php displayFlashMessage(); ?>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                Enter your email address and we'll send you instructions to reset your password.
            </div>
            
            <form method="POST" class="auth-form" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label for="email" class="form-label">Email Address *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-control" 
                            placeholder="Enter your email address"
                            value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                            required
                            autocomplete="email"
                        >
                    </div>
                </div>
                
                <button type="submit" class="btn-auth btn-primary">
                    <i class="fas fa-paper-plane"></i> Send Reset Instructions
                </button>
            </form>
        </div>
        
        <div class="auth-footer">
            <div class="mb-2">
                <a href="employee-login.php">
                    <i class="fas fa-arrow-left"></i> Back to Employee Login
                </a>
            </div>
            <div>
                <a href="../index.php">
                    <i class="fas fa-home"></i> Back to Home
                </a>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
