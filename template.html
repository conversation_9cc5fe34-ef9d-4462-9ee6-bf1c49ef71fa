<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>State University of Northern Negros Personal Data Sheet</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #2c3e50;
            text-align: center;
        }
        form {
            max-width: 800px;
            margin: 0 auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        p.warning {
            color: #d32f2f;
            font-weight: bold;
        }
        p.instructions {
            font-style: italic;
        }
        select, input[type="text"], input[type="checkbox"] {
            margin: 5px;
            padding: 5px;
            width: 150px;
        }
        td label {
            display: inline-block;
            width: 250px;
        }
        button {
            margin: 10px 0;
            padding: 8px;
            background-color: #2c3e50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #34495e;
        }
    </style>
    <script>
        function addGraduateStudyRow() {
            const table = document.getElementById('graduateStudiesTable');
            const row = table.insertRow(-1);
            row.innerHTML = `
                <td>
                    <select name="graduate_level">
                        <option value="Masters">Masters</option>
                        <option value="Doctoral">Doctoral</option>
                    </select>
                </td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text" style="width: 70px;"> - <input type="text" style="width: 70px;"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
            `;
        }
    </script>
</head>
<body>
    <h1>STATE UNIVERSITY OF NORTHERN NEGROS PERSONAL DATA SHEET</h1>
    <p class="warning">WARNING: Any misrepresentation made in the Personal Data Sheet and the Work Experience Sheet shall cause the filing of administrative/criminal case/s against the person concerned.</p>
    <p class="instructions">Print legibly. Tick appropriate boxes and use separate sheet if necessary. Indicate N/A if not applicable. DO NOT ABBREVIATE.</p>

    <form>
        <h2>I. PERSONAL INFORMATION</h2>
        <table>
            <tr>
                <th>FIELD</th>
                <th>DETAILS</th>
            </tr>
            <tr>
                <td>ID No. (For University use only)</td>
                <td><input type="text" disabled></td>
            </tr>
            <tr>
                <td>SURNAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>FIRST NAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>NAME EXTENSION (JR., SR)</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>MIDDLE NAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>DATE OF BIRTH (mm/dd/yyyy)</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>PLACE OF BIRTH</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>SEX</td>
                <td>
                    <input type="checkbox"> Male
                    <input type="checkbox"> Female
                </td>
            </tr>
            <tr>
                <td>CIVIL STATUS</td>
                <td>
                    <select name="civil_status">
                        <option value="">Select</option>
                        <option value="Single">Single</option>
                        <option value="Married">Married</option>
                        <option value="Widowed">Widowed</option>
                        <option value="Separated">Separated</option>
                        <option value="Others">Others</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>HEIGHT (m)</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>WEIGHT (kg)</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>BLOOD TYPE</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>GSIS ID NO.</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>PAG-IBIG ID NO.</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>PHILHEALTH NO.</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>SSS NO.</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>TIN NO.</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>EMPLOYEE NO.</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>CITIZENSHIP</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>If holder of dual citizenship, please indicate country</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>RESIDENTIAL ADDRESS</td>
                <td>
                    <label>House/Block/Lot No.: <input type="text"></label><br>
                    <label>Street: <input type="text"></label><br>
                    <label>Subdivision/Village: <input type="text"></label><br>
                    <label>Barangay: <input type="text"></label><br>
                    <label>City/Municipality: <input type="text"></label><br>
                    <label>Province: <input type="text"></label><br>
                    <label>ZIP CODE: <input type="text"></label>
                </td>
            </tr>
            <tr>
                <td>PERMANENT ADDRESS</td>
                <td>
                    <label>House/Block/Lot No.: <input type="text"></label><br>
                    <label>Street: <input type="text"></label><br>
                    <label>Subdivision/Village: <input type="text"></label><br>
                    <label>Barangay: <input type="text"></label><br>
                    <label>City/Municipality: <input type="text"></label><br>
                    <label>Province: <input type="text"></label><br>
                    <label>ZIP CODE: <input type="text"></label>
                </td>
            </tr>
            <tr>
                <td>TELEPHONE NO.</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>MOBILE NO.</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>E-MAIL ADDRESS</td>
                <td><input type="text"></td>
            </tr>
        </table>

        <h2>II. FAMILY BACKGROUND</h2>
        <table>
            <tr>
                <th>FIELD</th>
                <th>DETAILS</th>
            </tr>
            <tr>
                <td>SPOUSE'S SURNAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>FIRST NAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>NAME EXTENSION (JR., SR)</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>MIDDLE NAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>OCCUPATION</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>EMPLOYER/BUSINESS NAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>BUSINESS ADDRESS</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>TELEPHONE NO.</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>NAME OF CHILDREN</td>
                <td>
                    <label>Name: <input type="text"></label>
                    <label>DATE OF BIRTH (mm/dd/yyyy): <input type="text"></label><br>
                    <label>Name: <input type="text"></label>
                    <label>DATE OF BIRTH (mm/dd/yyyy): <input type="text"></label>
                </td>
            </tr>
            <tr>
                <td>FATHER'S SURNAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>FIRST NAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>NAME EXTENSION (JR., SR)</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>MIDDLE NAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>MOTHER'S MAIDEN NAME - SURNAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>FIRST NAME</td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>MIDDLE NAME</td>
                <td><input type="text"></td>
            </tr>
        </table>

        <h2>III. EDUCATIONAL BACKGROUND</h2>
        <table>
            <tr>
                <th>LEVEL</th>
                <th>NAME OF SCHOOL</th>
                <th>BASIC EDUCATION/DEGREE/COURSE</th>
                <th>PERIOD OF ATTENDANCE (From-To)</th>
                <th>HIGHEST LEVEL/UNITS EARNED (if not graduated)</th>
                <th>YEAR GRADUATED</th>
                <th>SCHOLARSHIP/ACADEMIC HONORS RECEIVED</th>
            </tr>
            <tr>
                <td>ELEMENTARY</td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text" style="width: 70px;"> - <input type="text" style="width: 70px;"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>SECONDARY</td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text" style="width: 70px;"> - <input type="text" style="width: 70px;"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>VOCATIONAL/TRADE COURSE</td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text" style="width: 70px;"> - <input type="text" style="width: 70px;"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
            </tr>
            <tr>
                <td>COLLEGE</td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text" style="width: 70px;"> - <input type="text" style="width: 70px;"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
            </tr>
        </table>
        <table id="graduateStudiesTable">
            <tr>
                <th>LEVEL</th>
                <th>NAME OF SCHOOL</th>
                <th>BASIC EDUCATION/DEGREE/COURSE</th>
                <th>PERIOD OF ATTENDANCE (From-To)</th>
                <th>HIGHEST LEVEL/UNITS EARNED (if not graduated)</th>
                <th>YEAR GRADUATED</th>
                <th>SCHOLARSHIP/ACADEMIC-HONORS-RECEIVED</th>
            </tr>
            <tr>
                <td>
                    <select name="graduate_level">
                        <option value="Masters">Masters</option>
                        <option value="Doctoral">Doctoral</option>
                    </select>
                </td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text" style="width: 70px;"> - <input type="text" style="width: 70px;"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
                <td><input type="text"></td>
            </tr>
        </table>
        <button type="button" onclick="addGraduateStudyRow()">Add Graduate Study</button>
    </form>
    <p>State University of Northern Negros PDS, Page 1 of 4</p>
</body>
</html>