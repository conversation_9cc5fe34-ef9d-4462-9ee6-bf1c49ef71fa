<?php
$page_title = "Employee Login";
include '../includes/header.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Validate CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        setFlashMessage('Security token mismatch. Please try again.', 'danger');
    }
    // Validate input
    elseif (empty($email) || empty($password)) {
        setFlashMessage('Please fill in all required fields.', 'danger');
    }
    elseif (!validateEmail($email)) {
        setFlashMessage('Please enter a valid email address.', 'danger');
    }
    else {
        // TODO: Implement actual authentication logic here
        // For now, this is just a template structure
        
        // Example authentication logic (replace with actual database check)
        if ($email === '<EMAIL>' && $password === 'password123') {
            // Set session variables
            $_SESSION['user_id'] = 1;
            $_SESSION['user_role'] = 'employee';
            $_SESSION['user_email'] = $email;
            $_SESSION['user_name'] = 'John Doe';
            
            // Handle remember me functionality
            if ($remember_me) {
                // Set secure cookie (implement proper token-based remember me)
                setcookie('remember_token', 'secure_token_here', time() + (30 * 24 * 60 * 60), '/', '', true, true);
            }
            
            setFlashMessage('Login successful! Welcome back.', 'success');
            redirect('../dashboard/employee-dashboard.php');
        } else {
            setFlashMessage('Invalid email or password. Please try again.', 'danger');
        }
    }
}
?>

<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h2><i class="fas fa-user"></i> Employee Login</h2>
            <div class="role-badge">Employee Portal</div>
        </div>
        
        <div class="auth-body">
            <?php displayFlashMessage(); ?>
            
            <form method="POST" class="auth-form" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label for="email" class="form-label">Email Address *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-control" 
                            placeholder="Enter your email address"
                            value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                            required
                            autocomplete="email"
                        >
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Password *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control" 
                            placeholder="Enter your password"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #7f8c8d; z-index: 3;">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="remember-me">
                    <input 
                        type="checkbox" 
                        id="remember_me" 
                        name="remember_me" 
                        value="1"
                        <?php echo isset($_POST['remember_me']) ? 'checked' : ''; ?>
                    >
                    <label for="remember_me">Remember me for 30 days</label>
                </div>
                
                <button type="submit" class="btn-auth btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Sign In
                </button>
            </form>
        </div>
        
        <div class="auth-footer">
            <div class="mb-2">
                <a href="forgot-password.php">
                    <i class="fas fa-key"></i> Forgot your password?
                </a>
            </div>
            <div class="mb-2">
                <a href="employee-register.php">
                    <i class="fas fa-user-plus"></i> New employee? Register here
                </a>
            </div>
            <div>
                <a href="../index.php">
                    <i class="fas fa-arrow-left"></i> Back to role selection
                </a>
            </div>
        </div>
    </div>
</div>

<?php
$additional_scripts = '
<script>
// Demo credentials helper
document.addEventListener("DOMContentLoaded", function() {
    // Add demo credentials info for testing
    const demoInfo = document.createElement("div");
    demoInfo.className = "alert alert-info mt-3";
    demoInfo.innerHTML = `
        <strong><i class="fas fa-info-circle"></i> Demo Credentials:</strong><br>
        Email: <EMAIL><br>
        Password: password123
    `;
    
    const authBody = document.querySelector(".auth-body");
    authBody.appendChild(demoInfo);
});
</script>
';

include '../includes/footer.php';
?>
