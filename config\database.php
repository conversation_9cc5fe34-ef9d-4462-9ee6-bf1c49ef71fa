<?php
/**
 * Database Configuration
 * 
 * This file contains database connection settings and helper functions
 * for the authentication system.
 */

// Database configuration constants
define('DB_HOST', 'localhost');
define('DB_NAME', 'corporate_auth');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

/**
 * Get database connection
 * 
 * @return PDO Database connection object
 * @throws PDOException If connection fails
 */
function getDBConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
            ];
            
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            // Log the error (in production, don't expose database errors)
            error_log("Database connection failed: " . $e->getMessage());
            throw new PDOException("Database connection failed");
        }
    }
    
    return $pdo;
}

/**
 * Execute a prepared statement
 * 
 * @param string $sql SQL query with placeholders
 * @param array $params Parameters for the query
 * @return PDOStatement
 */
function executeQuery($sql, $params = []) {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt;
}

/**
 * Get a single row from database
 * 
 * @param string $sql SQL query
 * @param array $params Query parameters
 * @return array|false
 */
function fetchRow($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetch();
}

/**
 * Get multiple rows from database
 * 
 * @param string $sql SQL query
 * @param array $params Query parameters
 * @return array
 */
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetchAll();
}

/**
 * Insert a new record and return the ID
 * 
 * @param string $sql SQL insert query
 * @param array $params Query parameters
 * @return string Last insert ID
 */
function insertRecord($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return getDBConnection()->lastInsertId();
}

/**
 * Check if a user exists by email
 * 
 * @param string $email User email
 * @return array|false User data or false if not found
 */
function getUserByEmail($email) {
    $sql = "SELECT * FROM users WHERE email = ? AND status = 'active'";
    return fetchRow($sql, [$email]);
}

/**
 * Check if a user exists by username
 * 
 * @param string $username Username
 * @return array|false User data or false if not found
 */
function getUserByUsername($username) {
    $sql = "SELECT * FROM users WHERE username = ? AND status = 'active'";
    return fetchRow($sql, [$username]);
}

/**
 * Check if an employee ID exists
 * 
 * @param string $employee_id Employee ID
 * @return bool True if exists, false otherwise
 */
function employeeIdExists($employee_id) {
    $sql = "SELECT COUNT(*) as count FROM users WHERE employee_id = ?";
    $result = fetchRow($sql, [$employee_id]);
    return $result['count'] > 0;
}

/**
 * Create a new user account
 * 
 * @param array $userData User data
 * @return string|false User ID or false on failure
 */
function createUser($userData) {
    $sql = "INSERT INTO users (
        first_name, last_name, email, employee_id, phone, 
        department, password_hash, role, status, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())";
    
    $params = [
        $userData['first_name'],
        $userData['last_name'],
        $userData['email'],
        $userData['employee_id'],
        $userData['phone'],
        $userData['department'],
        $userData['password_hash'],
        $userData['role'] ?? 'employee'
    ];
    
    try {
        return insertRecord($sql, $params);
    } catch (PDOException $e) {
        error_log("User creation failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Update user login timestamp
 * 
 * @param int $user_id User ID
 * @return bool Success status
 */
function updateLastLogin($user_id) {
    $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
    try {
        executeQuery($sql, [$user_id]);
        return true;
    } catch (PDOException $e) {
        error_log("Failed to update last login: " . $e->getMessage());
        return false;
    }
}

/**
 * Store password reset token
 * 
 * @param string $email User email
 * @param string $token Reset token
 * @param int $expires Expiration timestamp
 * @return bool Success status
 */
function storePasswordResetToken($email, $token, $expires) {
    // First, delete any existing tokens for this email
    $deleteSql = "DELETE FROM password_resets WHERE email = ?";
    executeQuery($deleteSql, [$email]);
    
    // Insert new token
    $sql = "INSERT INTO password_resets (email, token, expires_at, created_at) VALUES (?, ?, FROM_UNIXTIME(?), NOW())";
    try {
        executeQuery($sql, [$email, $token, $expires]);
        return true;
    } catch (PDOException $e) {
        error_log("Failed to store password reset token: " . $e->getMessage());
        return false;
    }
}

/**
 * Verify password reset token
 * 
 * @param string $token Reset token
 * @return array|false Token data or false if invalid
 */
function verifyPasswordResetToken($token) {
    $sql = "SELECT * FROM password_resets WHERE token = ? AND expires_at > NOW()";
    return fetchRow($sql, [$token]);
}

/**
 * Database schema creation (for initial setup)
 * This would typically be run once during installation
 */
function createDatabaseSchema() {
    $pdo = getDBConnection();
    
    // Users table
    $usersSql = "
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        username VARCHAR(50) UNIQUE,
        employee_id VARCHAR(20) UNIQUE,
        phone VARCHAR(20),
        department VARCHAR(50),
        password_hash VARCHAR(255) NOT NULL,
        role ENUM('employee', 'admin', 'pdc') DEFAULT 'employee',
        status ENUM('active', 'pending', 'suspended') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL
    )";
    
    // Password resets table
    $resetsSql = "
    CREATE TABLE IF NOT EXISTS password_resets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(100) NOT NULL,
        token VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        used_at TIMESTAMP NULL
    )";
    
    // Login attempts table (for security)
    $attemptsSql = "
    CREATE TABLE IF NOT EXISTS login_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(100),
        ip_address VARCHAR(45),
        success BOOLEAN DEFAULT FALSE,
        attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    try {
        $pdo->exec($usersSql);
        $pdo->exec($resetsSql);
        $pdo->exec($attemptsSql);
        return true;
    } catch (PDOException $e) {
        error_log("Schema creation failed: " . $e->getMessage());
        return false;
    }
}
?>
