<?php
$page_title = "PDC Login";
include '../includes/header.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Validate CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        setFlashMessage('Security token mismatch. Please try again.', 'danger');
    }
    // Validate input
    elseif (empty($email) || empty($password)) {
        setFlashMessage('Please fill in all required fields.', 'danger');
    }
    elseif (!validateEmail($email)) {
        setFlashMessage('Please enter a valid email address.', 'danger');
    }
    else {
        // TODO: Implement actual authentication logic here
        // For now, this is just a template structure
        
        // Example authentication logic (replace with actual database check)
        if ($email === '<EMAIL>' && $password === 'pdc123') {
            // Set session variables
            $_SESSION['user_id'] = 3;
            $_SESSION['user_role'] = 'pdc';
            $_SESSION['user_email'] = $email;
            $_SESSION['user_name'] = 'PDC Member';
            
            // Handle remember me functionality
            if ($remember_me) {
                // Set secure cookie (implement proper token-based remember me)
                setcookie('remember_token', 'secure_pdc_token_here', time() + (30 * 24 * 60 * 60), '/', '', true, true);
            }
            
            setFlashMessage('PDC login successful! Welcome to the development committee portal.', 'success');
            redirect('../dashboard/pdc-dashboard.php');
        } else {
            setFlashMessage('Invalid email or password. Please try again.', 'danger');
        }
    }
}
?>

<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header" style="background: linear-gradient(135deg, #27ae60, #229954);">
            <h2><i class="fas fa-users-cog"></i> PDC Login</h2>
            <div class="role-badge">Personal Development Committee</div>
        </div>
        
        <div class="auth-body">
            <?php displayFlashMessage(); ?>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>PDC Portal:</strong> Access development programs, training resources, and committee functions.
            </div>
            
            <form method="POST" class="auth-form" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label for="email" class="form-label">Email Address *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-control" 
                            placeholder="Enter your PDC email address"
                            value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                            required
                            autocomplete="email"
                        >
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Password *</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="form-control"
                            placeholder="Enter your password"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" aria-label="Toggle password visibility">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="remember-me">
                    <input 
                        type="checkbox" 
                        id="remember_me" 
                        name="remember_me" 
                        value="1"
                        <?php echo isset($_POST['remember_me']) ? 'checked' : ''; ?>
                    >
                    <label for="remember_me">Remember me for 30 days</label>
                </div>
                
                <button type="submit" class="btn-auth" style="background: #27ae60; color: white;">
                    <i class="fas fa-sign-in-alt"></i> PDC Sign In
                </button>
            </form>
            
            <div class="mt-3 p-3" style="background: #e8f5e8; border-radius: 8px;">
                <small class="text-success">
                    <i class="fas fa-graduation-cap"></i>
                    <strong>Development Focus:</strong> Empowering growth through continuous learning and development.
                </small>
            </div>
        </div>
        
        <div class="auth-footer">
            <div class="mb-2">
                <a href="pdc-forgot-password.php">
                    <i class="fas fa-key"></i> Forgot your password?
                </a>
            </div>
            <div class="mb-2">
                <a href="pdc-resources.php">
                    <i class="fas fa-book"></i> Access public resources
                </a>
            </div>
            <div>
                <a href="../index.php">
                    <i class="fas fa-arrow-left"></i> Back to role selection
                </a>
            </div>
        </div>
    </div>
</div>

<?php
$additional_scripts = '
<script>
// Demo credentials helper for PDC
document.addEventListener("DOMContentLoaded", function() {
    // Add demo credentials info for testing
    const demoInfo = document.createElement("div");
    demoInfo.className = "alert alert-info mt-3";
    demoInfo.innerHTML = `
        <strong><i class="fas fa-info-circle"></i> Demo Credentials:</strong><br>
        Email: <EMAIL><br>
        Password: pdc123
    `;
    
    const authBody = document.querySelector(".auth-body");
    authBody.appendChild(demoInfo);
    
    // Add development tip
    setTimeout(function() {
        const tip = document.createElement("div");
        tip.className = "alert alert-success";
        tip.innerHTML = `
            <i class="fas fa-lightbulb"></i>
            <strong>Tip:</strong> Check out the latest training modules and development opportunities in your dashboard.
        `;
        
        const form = document.querySelector(".auth-form");
        form.parentNode.insertBefore(tip, form);
    }, 3000);
});
</script>
';

include '../includes/footer.php';
?>
