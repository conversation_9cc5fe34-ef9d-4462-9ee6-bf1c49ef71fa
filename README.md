# Corporate Authentication System

A comprehensive, multi-role authentication system built with PHP and Bootstrap 5, designed for corporate environments with separate login portals for different user types.

## Features

### 🔐 Multi-Role Authentication
- **Employee Portal**: Self-registration and login for employees
- **Admin/HR Portal**: Administrative access with enhanced security
- **PDC Portal**: Personal Development Committee access

### 🎨 Modern Design
- Responsive Bootstrap 5 interface
- Professional corporate styling
- Mobile-first design approach
- Consistent branding across all pages

### 🛡️ Security Features
- CSRF protection on all forms
- Password strength validation
- Secure session management
- Input sanitization and validation
- Remember me functionality with secure tokens

### 📱 Responsive Design
- Works seamlessly on desktop, tablet, and mobile
- Touch-friendly interface elements
- Optimized for various screen sizes

## File Structure

```
template/
├── assets/
│   ├── css/
│   │   └── auth-styles.css          # Custom styling
│   └── js/
│       └── auth-validation.js       # Client-side validation
├── auth/
│   ├── employee-login.php           # Employee login page
│   ├── admin-login.php              # Admin/HR login page
│   ├── pdc-login.php                # PDC login page
│   ├── employee-register.php        # Employee registration
│   └── forgot-password.php          # Password recovery
├── config/
│   └── database.php                 # Database configuration
├── dashboard/
│   ├── employee-dashboard.php       # Employee dashboard
│   └── logout.php                   # Logout functionality
├── includes/
│   ├── header.php                   # Common header and functions
│   └── footer.php                   # Common footer
└── index.php                        # Main landing page
```

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher (optional, for full functionality)
- Web server (Apache, Nginx, or PHP built-in server)

### Quick Start

1. **Clone or download** the files to your web server directory

2. **Start the development server**:
   ```bash
   php -S localhost:8000
   ```

3. **Open your browser** and navigate to:
   ```
   http://localhost:8000
   ```

### Database Setup (Optional)

For full functionality with user persistence:

1. Create a MySQL database named `corporate_auth`
2. Update database credentials in `config/database.php`
3. Run the schema creation (uncomment and execute `createDatabaseSchema()`)

## Demo Credentials

### Employee Login
- **Email**: <EMAIL>
- **Password**: password123

### Admin/HR Login
- **Username**: admin
- **Password**: admin123

### PDC Login
- **Email**: <EMAIL>
- **Password**: pdc123

## Usage

### For Employees
1. Visit the main portal
2. Click "Employee" to access the employee login
3. New employees can register using the registration form
4. Access personal dashboard after login

### For Administrators
1. Select "Admin/HR" from the main portal
2. Use administrative credentials
3. Access enhanced administrative functions

### For PDC Members
1. Choose "PDC" from the role selection
2. Login with PDC credentials
3. Access development and training resources

## Customization

### Styling
- Modify `assets/css/auth-styles.css` for custom styling
- Update CSS variables in `:root` for color scheme changes
- Bootstrap classes can be overridden as needed

### Branding
- Update company name and logo in `includes/header.php`
- Modify color scheme in CSS variables
- Add custom favicon and images to `assets/images/`

### Functionality
- Extend user roles by modifying the role enum
- Add new form fields in registration
- Customize validation rules in JavaScript and PHP

## Security Considerations

### Implemented Security Features
- ✅ CSRF token protection
- ✅ Input sanitization
- ✅ Password strength requirements
- ✅ Secure session handling
- ✅ SQL injection prevention (prepared statements)
- ✅ XSS protection (output escaping)

### Production Recommendations
- [ ] Enable HTTPS in production
- [ ] Implement rate limiting for login attempts
- [ ] Add email verification for registration
- [ ] Set up proper error logging
- [ ] Configure secure session settings
- [ ] Implement password reset via email
- [ ] Add two-factor authentication (2FA)

## Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Dependencies

### Frontend
- Bootstrap 5.3.2 (CDN)
- Font Awesome 6.4.0 (CDN)
- Custom CSS and JavaScript

### Backend
- PHP 7.4+
- PDO MySQL extension (for database functionality)

## Development

### Adding New User Roles
1. Update the role enum in database schema
2. Create new login page in `auth/` directory
3. Add role-specific styling and validation
4. Create corresponding dashboard
5. Update role selection on main page

### Extending Forms
1. Add new fields to HTML forms
2. Update validation in `auth-validation.js`
3. Add server-side validation in PHP
4. Update database schema if needed

## Troubleshooting

### Common Issues

**CSS/JS not loading**
- Check file paths in `includes/header.php`
- Ensure assets directory is accessible
- Verify web server configuration

**Database connection errors**
- Update credentials in `config/database.php`
- Ensure MySQL service is running
- Check database permissions

**Session issues**
- Verify PHP session configuration
- Check file permissions for session storage
- Ensure cookies are enabled in browser

## License

This project is provided as a template for educational and commercial use. Feel free to modify and adapt for your specific needs.

## Support

For questions or issues:
1. Check the troubleshooting section
2. Review the code comments for implementation details
3. Test with demo credentials first
4. Ensure all prerequisites are met

---

**Note**: This is a front-end focused template with basic PHP structure. For production use, implement proper database integration, email services, and additional security measures as needed.
